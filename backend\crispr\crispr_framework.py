"""
CRISPR Framework - Comprehensive CRISPR-Cas System Analysis Framework
Integrates all CRISPR components for complete analysis pipeline
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import logging

from .grna_designer import GuideRNADesigner, GuideRNA
from .cas_protein_modeler import Cas<PERSON><PERSON>in<PERSON><PERSON><PERSON>, CasProteinStructure
from .pam_recognizer import PAMRecognizer, PAMSite
from .offtarget_predictor import OffTargetPredictor, OffTargetSite
from .efficiency_scorer import CRISPREfficiencyScorer, EfficiencyScore

logger = logging.getLogger(__name__)

@dataclass
class CRISPRAnalysisResult:
    """Complete CRISPR analysis result"""
    target_sequence: str
    cas_type: str
    guide_rnas: List[GuideRNA]
    pam_sites: List[PAMSite]
    off_targets: Dict[str, List[OffTargetSite]]  # gRNA sequence -> off-targets
    efficiency_scores: Dict[str, EfficiencyScore]  # gRNA sequence -> efficiency
    cas_structure: Optional[CasProteinStructure]
    recommendations: List[str]
    summary_stats: Dict[str, float]

class CRISPRFramework:
    """
    Comprehensive CRISPR-Cas system analysis framework
    """
    
    def __init__(self, cas_type: str = "Cas9", reference_genome: str = None):
        self.cas_type = cas_type
        self.reference_genome = reference_genome
        
        # Initialize all components
        self.grna_designer = GuideRNADesigner(cas_type=cas_type)
        self.cas_modeler = CasProteinModeler()
        self.pam_recognizer = PAMRecognizer()
        self.offtarget_predictor = OffTargetPredictor(reference_genome=reference_genome)
        self.efficiency_scorer = CRISPREfficiencyScorer(cas_type=cas_type)
        
        logger.info(f"Initialized CRISPR Framework for {cas_type}")
    
    def analyze_target(self, target_sequence: str, num_grnas: int = 10,
                      analyze_offtargets: bool = True, 
                      max_offtarget_mismatches: int = 4) -> CRISPRAnalysisResult:
        """
        Perform comprehensive CRISPR analysis on target sequence
        
        Args:
            target_sequence: DNA sequence to analyze
            num_grnas: Number of top gRNAs to design
            analyze_offtargets: Whether to perform off-target analysis
            max_offtarget_mismatches: Maximum mismatches for off-target search
            
        Returns:
            CRISPRAnalysisResult with complete analysis
        """
        logger.info(f"Starting comprehensive CRISPR analysis for {len(target_sequence)}bp sequence")
        
        # Step 1: Find PAM sites
        logger.info("Step 1: Finding PAM sites...")
        pam_sites = self.pam_recognizer.find_pam_sites(target_sequence, self.cas_type)
        
        # Step 2: Design guide RNAs
        logger.info("Step 2: Designing guide RNAs...")
        guide_rnas = self.grna_designer.design_grnas(target_sequence, num_grnas)
        
        # Step 3: Score efficiency for each gRNA
        logger.info("Step 3: Scoring gRNA efficiency...")
        efficiency_scores = {}
        for grna in guide_rnas:
            score = self.efficiency_scorer.score_efficiency(grna.sequence, target_sequence)
            efficiency_scores[grna.sequence] = score
        
        # Step 4: Predict off-targets
        off_targets = {}
        if analyze_offtargets:
            logger.info("Step 4: Predicting off-targets...")
            for grna in guide_rnas:
                offtargets = self.offtarget_predictor.predict_off_targets(
                    grna.sequence, target_sequence, max_offtarget_mismatches
                )
                off_targets[grna.sequence] = offtargets
        
        # Step 5: Model Cas protein structure
        logger.info("Step 5: Modeling Cas protein...")
        cas_structure = None
        try:
            cas_structure = self.cas_modeler.model_cas_protein(self.cas_type)
        except Exception as e:
            logger.warning(f"Could not model Cas protein: {e}")
        
        # Step 6: Generate recommendations
        logger.info("Step 6: Generating recommendations...")
        recommendations = self._generate_recommendations(
            guide_rnas, efficiency_scores, off_targets, pam_sites
        )
        
        # Step 7: Calculate summary statistics
        summary_stats = self._calculate_summary_stats(
            guide_rnas, efficiency_scores, off_targets, pam_sites
        )
        
        result = CRISPRAnalysisResult(
            target_sequence=target_sequence,
            cas_type=self.cas_type,
            guide_rnas=guide_rnas,
            pam_sites=pam_sites,
            off_targets=off_targets,
            efficiency_scores=efficiency_scores,
            cas_structure=cas_structure,
            recommendations=recommendations,
            summary_stats=summary_stats
        )
        
        logger.info("CRISPR analysis completed successfully")
        return result
    
    def compare_cas_systems(self, target_sequence: str, 
                          cas_types: List[str] = None) -> Dict[str, CRISPRAnalysisResult]:
        """
        Compare multiple Cas systems for the same target
        
        Args:
            target_sequence: DNA sequence to analyze
            cas_types: List of Cas types to compare
            
        Returns:
            Dictionary mapping Cas type to analysis results
        """
        if cas_types is None:
            cas_types = ["Cas9", "Cas12a", "Cas13a"]
        
        logger.info(f"Comparing {len(cas_types)} Cas systems")
        
        results = {}
        original_cas_type = self.cas_type
        
        for cas_type in cas_types:
            logger.info(f"Analyzing with {cas_type}...")
            
            # Temporarily switch Cas type
            self.cas_type = cas_type
            self.grna_designer.cas_type = cas_type
            self.efficiency_scorer.cas_type = cas_type
            
            # Perform analysis
            result = self.analyze_target(target_sequence, num_grnas=5, analyze_offtargets=False)
            results[cas_type] = result
        
        # Restore original Cas type
        self.cas_type = original_cas_type
        self.grna_designer.cas_type = original_cas_type
        self.efficiency_scorer.cas_type = original_cas_type
        
        return results
    
    def optimize_grna_design(self, target_sequence: str, 
                           optimization_criteria: Dict[str, float] = None) -> List[GuideRNA]:
        """
        Optimize gRNA design based on custom criteria
        
        Args:
            target_sequence: DNA sequence to target
            optimization_criteria: Weights for different criteria
            
        Returns:
            List of optimized GuideRNA objects
        """
        if optimization_criteria is None:
            optimization_criteria = {
                'efficiency': 0.4,
                'specificity': 0.3,
                'accessibility': 0.2,
                'conservation': 0.1
            }
        
        logger.info("Optimizing gRNA design with custom criteria")
        
        # Design initial set of gRNAs
        guide_rnas = self.grna_designer.design_grnas(target_sequence, num_grnas=50)
        
        # Score each gRNA with custom criteria
        optimized_grnas = []
        for grna in guide_rnas:
            # Get efficiency score
            efficiency = self.efficiency_scorer.score_efficiency(grna.sequence)
            
            # Calculate custom score
            custom_score = (
                optimization_criteria.get('efficiency', 0) * efficiency.ensemble_score +
                optimization_criteria.get('specificity', 0) * grna.off_target_score +
                optimization_criteria.get('accessibility', 0) * 0.8 +  # Placeholder
                optimization_criteria.get('conservation', 0) * 0.7     # Placeholder
            )
            
            # Update gRNA with custom score
            grna.total_score = custom_score
            optimized_grnas.append(grna)
        
        # Sort by custom score
        optimized_grnas.sort(key=lambda x: x.total_score, reverse=True)
        
        return optimized_grnas[:10]  # Return top 10
    
    def _generate_recommendations(self, guide_rnas: List[GuideRNA], 
                                efficiency_scores: Dict[str, EfficiencyScore],
                                off_targets: Dict[str, List[OffTargetSite]],
                                pam_sites: List[PAMSite]) -> List[str]:
        """Generate analysis recommendations"""
        recommendations = []
        
        if not guide_rnas:
            recommendations.append("⚠️ No suitable guide RNAs found. Consider using a different Cas system.")
            return recommendations
        
        # Best gRNA recommendation
        best_grna = guide_rnas[0]
        recommendations.append(f"🎯 Top recommended gRNA: {best_grna.sequence} (score: {best_grna.total_score:.3f})")
        
        # Efficiency analysis
        if best_grna.sequence in efficiency_scores:
            eff_score = efficiency_scores[best_grna.sequence]
            if eff_score.ensemble_score > 0.7:
                recommendations.append("✅ High predicted cutting efficiency")
            elif eff_score.ensemble_score < 0.3:
                recommendations.append("⚠️ Low predicted cutting efficiency - consider alternative gRNAs")
        
        # Off-target analysis
        if best_grna.sequence in off_targets:
            offtargets = off_targets[best_grna.sequence]
            high_risk = [ot for ot in offtargets if ot.score > 0.5]
            if not high_risk:
                recommendations.append("✅ Low off-target risk")
            else:
                recommendations.append(f"⚠️ {len(high_risk)} high-risk off-target sites detected")
        
        # PAM availability
        high_quality_pams = [pam for pam in pam_sites if pam.score > 0.8]
        if len(high_quality_pams) > 10:
            recommendations.append("✅ Abundant high-quality PAM sites available")
        elif len(high_quality_pams) < 3:
            recommendations.append("⚠️ Limited high-quality PAM sites - consider alternative Cas system")
        
        # GC content analysis
        avg_gc = np.mean([grna.gc_content for grna in guide_rnas[:5]])
        if 40 <= avg_gc <= 60:
            recommendations.append("✅ Optimal GC content range")
        else:
            recommendations.append("⚠️ Suboptimal GC content - may affect efficiency")
        
        return recommendations
    
    def _calculate_summary_stats(self, guide_rnas: List[GuideRNA],
                               efficiency_scores: Dict[str, EfficiencyScore],
                               off_targets: Dict[str, List[OffTargetSite]],
                               pam_sites: List[PAMSite]) -> Dict[str, float]:
        """Calculate summary statistics"""
        stats = {}
        
        if guide_rnas:
            stats['num_grnas'] = len(guide_rnas)
            stats['avg_grna_score'] = np.mean([grna.total_score for grna in guide_rnas])
            stats['avg_gc_content'] = np.mean([grna.gc_content for grna in guide_rnas])
            stats['best_grna_score'] = guide_rnas[0].total_score
        
        if efficiency_scores:
            ensemble_scores = [score.ensemble_score for score in efficiency_scores.values()]
            stats['avg_efficiency'] = np.mean(ensemble_scores)
            stats['max_efficiency'] = np.max(ensemble_scores)
        
        if off_targets:
            total_offtargets = sum(len(ots) for ots in off_targets.values())
            stats['total_offtargets'] = total_offtargets
            if total_offtargets > 0:
                all_scores = [ot.score for ots in off_targets.values() for ot in ots]
                stats['avg_offtarget_score'] = np.mean(all_scores)
        
        if pam_sites:
            stats['num_pam_sites'] = len(pam_sites)
            stats['avg_pam_score'] = np.mean([pam.score for pam in pam_sites])
        
        return stats
    
    def export_results(self, result: CRISPRAnalysisResult, 
                      format: str = "json") -> str:
        """Export analysis results in specified format"""
        if format == "json":
            import json
            # Convert to JSON-serializable format
            data = {
                'cas_type': result.cas_type,
                'target_length': len(result.target_sequence),
                'num_grnas': len(result.guide_rnas),
                'recommendations': result.recommendations,
                'summary_stats': result.summary_stats,
                'top_grnas': [
                    {
                        'sequence': grna.sequence,
                        'position': grna.position,
                        'score': grna.total_score,
                        'gc_content': grna.gc_content
                    }
                    for grna in result.guide_rnas[:5]
                ]
            }
            return json.dumps(data, indent=2)
        
        elif format == "csv":
            # Generate CSV format
            lines = ["gRNA_sequence,position,strand,score,gc_content,efficiency"]
            for grna in result.guide_rnas:
                eff_score = result.efficiency_scores.get(grna.sequence)
                efficiency = eff_score.ensemble_score if eff_score else 0.0
                lines.append(f"{grna.sequence},{grna.position},{grna.strand},"
                           f"{grna.total_score:.3f},{grna.gc_content:.1f},{efficiency:.3f}")
            return "\n".join(lines)
        
        else:
            raise ValueError(f"Unsupported export format: {format}")
