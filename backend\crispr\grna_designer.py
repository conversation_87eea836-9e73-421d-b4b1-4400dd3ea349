"""
Guide RNA Designer - Automated gRNA Design and Optimization
Provides comprehensive gRNA design with scoring and optimization
"""

import re
import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from Bio.Seq import Seq
from Bio.SeqUtils import GC
import logging

logger = logging.getLogger(__name__)

@dataclass
class GuideRNA:
    """Guide RNA data structure"""
    sequence: str
    position: int
    strand: str  # '+' or '-'
    pam_sequence: str
    gc_content: float
    on_target_score: float
    off_target_score: float
    efficiency_score: float
    
    def __post_init__(self):
        self.total_score = (self.on_target_score * 0.4 + 
                           self.off_target_score * 0.3 + 
                           self.efficiency_score * 0.3)

class GuideRNADesigner:
    """
    Comprehensive guide RNA design system with multiple scoring algorithms
    """
    
    def __init__(self, cas_type: str = "Cas9"):
        self.cas_type = cas_type
        self.grna_length = 20  # Standard gRNA length
        
        # PAM sequences for different Cas proteins
        self.pam_patterns = {
            "Cas9": r"[ATCG]{20}[ATCG]GG",  # NGG PAM
            "Cas12a": r"TTT[ATCG][ATCG]{20}",  # TTTV PAM  
            "Cas13": r"[ATCG]{28}",  # No specific PAM for Cas13
        }
        
        # Initialize scoring parameters
        self._init_scoring_parameters()
        
    def _init_scoring_parameters(self):
        """Initialize parameters for gRNA scoring"""
        # Position-specific weights for on-target scoring
        self.position_weights = np.array([
            0.0, 0.0, 0.014, 0.0, 0.0, 0.395, 0.317, 0.0, 0.389, 0.079,
            0.445, 0.508, 0.613, 0.851, 0.732, 0.828, 0.615, 0.804, 0.685, 0.583
        ])
        
        # Nucleotide preferences at each position
        self.nucleotide_preferences = {
            'A': [0.25] * 20, 'T': [0.25] * 20, 
            'G': [0.25] * 20, 'C': [0.25] * 20
        }
        
    def design_grnas(self, target_sequence: str, num_grnas: int = 10) -> List[GuideRNA]:
        """
        Design guide RNAs for a target sequence
        
        Args:
            target_sequence: DNA sequence to target
            num_grnas: Number of top gRNAs to return
            
        Returns:
            List of GuideRNA objects sorted by score
        """
        logger.info(f"Designing gRNAs for {len(target_sequence)}bp sequence")
        
        # Find all potential gRNA sites
        potential_grnas = self._find_grna_sites(target_sequence)
        
        # Score each gRNA
        scored_grnas = []
        for grna_data in potential_grnas:
            grna = self._score_grna(grna_data, target_sequence)
            if grna:
                scored_grnas.append(grna)
        
        # Sort by total score and return top candidates
        scored_grnas.sort(key=lambda x: x.total_score, reverse=True)
        
        logger.info(f"Generated {len(scored_grnas)} gRNAs, returning top {num_grnas}")
        return scored_grnas[:num_grnas]
    
    def _find_grna_sites(self, sequence: str) -> List[Dict]:
        """Find all potential gRNA binding sites"""
        sites = []
        pattern = self.pam_patterns.get(self.cas_type, self.pam_patterns["Cas9"])
        
        # Search forward strand
        for match in re.finditer(pattern, sequence):
            if self.cas_type == "Cas9":
                grna_seq = match.group()[:20]
                pam_seq = match.group()[20:]
                position = match.start()
            elif self.cas_type == "Cas12a":
                grna_seq = match.group()[4:]
                pam_seq = match.group()[:4]
                position = match.start() + 4
            else:  # Cas13
                grna_seq = match.group()[:20]
                pam_seq = ""
                position = match.start()
                
            sites.append({
                'sequence': grna_seq,
                'position': position,
                'strand': '+',
                'pam': pam_seq
            })
        
        # Search reverse strand
        rev_comp = str(Seq(sequence).reverse_complement())
        for match in re.finditer(pattern, rev_comp):
            if self.cas_type == "Cas9":
                grna_seq = match.group()[:20]
                pam_seq = match.group()[20:]
                position = len(sequence) - match.end()
            elif self.cas_type == "Cas12a":
                grna_seq = match.group()[4:]
                pam_seq = match.group()[:4]
                position = len(sequence) - match.end() + 4
            else:  # Cas13
                grna_seq = match.group()[:20]
                pam_seq = ""
                position = len(sequence) - match.end()
                
            sites.append({
                'sequence': grna_seq,
                'position': position,
                'strand': '-',
                'pam': pam_seq
            })
        
        return sites
    
    def _score_grna(self, grna_data: Dict, target_sequence: str) -> Optional[GuideRNA]:
        """Score a single gRNA candidate"""
        try:
            sequence = grna_data['sequence']
            
            # Basic quality filters
            if len(sequence) != self.grna_length:
                return None
                
            # Calculate GC content
            gc_content = GC(sequence)
            if gc_content < 20 or gc_content > 80:  # Filter extreme GC content
                return None
            
            # Calculate scores
            on_target_score = self._calculate_on_target_score(sequence)
            off_target_score = self._calculate_off_target_score(sequence, target_sequence)
            efficiency_score = self._calculate_efficiency_score(sequence)
            
            return GuideRNA(
                sequence=sequence,
                position=grna_data['position'],
                strand=grna_data['strand'],
                pam_sequence=grna_data['pam'],
                gc_content=gc_content,
                on_target_score=on_target_score,
                off_target_score=off_target_score,
                efficiency_score=efficiency_score
            )
            
        except Exception as e:
            logger.warning(f"Error scoring gRNA {grna_data.get('sequence', 'unknown')}: {e}")
            return None
    
    def _calculate_on_target_score(self, sequence: str) -> float:
        """Calculate on-target activity score using Doench 2016 algorithm"""
        score = 0.0
        
        # Position-specific scoring
        for i, nucleotide in enumerate(sequence):
            if i < len(self.position_weights):
                weight = self.position_weights[i]
                # Simple nucleotide preference scoring
                if nucleotide in ['G', 'C']:
                    score += weight * 0.6
                else:
                    score += weight * 0.4
        
        # Penalize poly-T runs (>3 consecutive T's)
        if 'TTTT' in sequence:
            score *= 0.5
            
        # Normalize to 0-1 range
        return min(max(score, 0.0), 1.0)
    
    def _calculate_off_target_score(self, sequence: str, target_sequence: str) -> float:
        """Calculate off-target specificity score"""
        # Simplified off-target scoring based on sequence complexity
        
        # Penalize low complexity sequences
        unique_kmers = len(set([sequence[i:i+3] for i in range(len(sequence)-2)]))
        complexity_score = unique_kmers / (len(sequence) - 2)
        
        # Penalize repetitive sequences
        max_repeat = max([len(match.group()) for match in re.finditer(r'(.)\1+', sequence)] + [1])
        repeat_penalty = 1.0 / (1.0 + max_repeat - 1)
        
        # Combined off-target score
        off_target_score = complexity_score * repeat_penalty
        
        return min(max(off_target_score, 0.0), 1.0)
    
    def _calculate_efficiency_score(self, sequence: str) -> float:
        """Calculate cutting efficiency score"""
        # Based on nucleotide composition and position
        efficiency = 0.5  # Base efficiency
        
        # Prefer G/C at positions 17-20 (seed region)
        seed_region = sequence[16:20] if len(sequence) >= 20 else sequence[-4:]
        gc_in_seed = sum(1 for nt in seed_region if nt in 'GC')
        efficiency += (gc_in_seed / len(seed_region)) * 0.3
        
        # Penalize A/T rich regions
        at_content = sum(1 for nt in sequence if nt in 'AT') / len(sequence)
        if at_content > 0.7:
            efficiency *= 0.8
            
        return min(max(efficiency, 0.0), 1.0)
    
    def optimize_grna_set(self, grnas: List[GuideRNA], max_set_size: int = 5) -> List[GuideRNA]:
        """
        Optimize a set of gRNAs for minimal off-target overlap
        
        Args:
            grnas: List of candidate gRNAs
            max_set_size: Maximum number of gRNAs in optimized set
            
        Returns:
            Optimized set of gRNAs
        """
        if len(grnas) <= max_set_size:
            return grnas
        
        # Greedy selection algorithm
        selected = [grnas[0]]  # Start with best gRNA
        remaining = grnas[1:]
        
        while len(selected) < max_set_size and remaining:
            # Find gRNA with minimal similarity to selected set
            best_candidate = None
            best_score = -1
            
            for candidate in remaining:
                # Calculate diversity score
                min_similarity = min([
                    self._calculate_similarity(candidate.sequence, selected_grna.sequence)
                    for selected_grna in selected
                ])
                
                # Combined score: individual quality + diversity
                combined_score = candidate.total_score * 0.7 + (1 - min_similarity) * 0.3
                
                if combined_score > best_score:
                    best_score = combined_score
                    best_candidate = candidate
            
            if best_candidate:
                selected.append(best_candidate)
                remaining.remove(best_candidate)
        
        return selected
    
    def _calculate_similarity(self, seq1: str, seq2: str) -> float:
        """Calculate sequence similarity between two gRNAs"""
        if len(seq1) != len(seq2):
            return 0.0
        
        matches = sum(1 for a, b in zip(seq1, seq2) if a == b)
        return matches / len(seq1)
    
    def export_grnas(self, grnas: List[GuideRNA], format: str = "csv") -> str:
        """Export gRNAs in specified format"""
        if format == "csv":
            lines = ["Sequence,Position,Strand,PAM,GC_Content,OnTarget_Score,OffTarget_Score,Efficiency_Score,Total_Score"]
            for grna in grnas:
                lines.append(f"{grna.sequence},{grna.position},{grna.strand},{grna.pam_sequence},"
                           f"{grna.gc_content:.2f},{grna.on_target_score:.3f},{grna.off_target_score:.3f},"
                           f"{grna.efficiency_score:.3f},{grna.total_score:.3f}")
            return "\n".join(lines)
        
        elif format == "fasta":
            lines = []
            for i, grna in enumerate(grnas):
                lines.append(f">gRNA_{i+1}_pos{grna.position}_score{grna.total_score:.3f}")
                lines.append(grna.sequence)
            return "\n".join(lines)
        
        else:
            raise ValueError(f"Unsupported format: {format}")