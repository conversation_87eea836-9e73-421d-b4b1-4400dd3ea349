"""
CRISPR Efficiency Scorer - Advanced Efficiency Prediction for CRISPR Systems
Provides multiple algorithms for predicting CRISPR cutting efficiency
"""

import numpy as np
import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from Bio.Seq import Seq
from Bio.SeqUtils import GC
import logging

logger = logging.getLogger(__name__)

@dataclass
class EfficiencyScore:
    """Efficiency score data structure"""
    grna_sequence: str
    doench2016_score: float
    doench2014_score: float
    wang2019_score: float
    kim2018_score: float
    ensemble_score: float
    confidence: float

class CRISPREfficiencyScorer:
    """
    Advanced CRISPR efficiency scoring using multiple algorithms
    """
    
    def __init__(self, cas_type: str = "Cas9"):
        self.cas_type = cas_type
        
        # Doench 2016 parameters (Rule Set 2)
        self.doench2016_params = self._init_doench2016_params()
        
        # Doench 2014 parameters (Rule Set 1)
        self.doench2014_params = self._init_doench2014_params()
        
        # Wang 2019 parameters (DeepCRISPR)
        self.wang2019_params = self._init_wang2019_params()
        
        # Kim 2018 parameters (Cas12a efficiency)
        self.kim2018_params = self._init_kim2018_params()
        
    def score_efficiency(self, grna_sequence: str, context_sequence: str = None) -> EfficiencyScore:
        """
        Calculate efficiency scores using multiple algorithms
        
        Args:
            grna_sequence: Guide RNA sequence (20 nt for Cas9)
            context_sequence: Extended context sequence (optional)
            
        Returns:
            EfficiencyScore object with multiple algorithm scores
        """
        if len(grna_sequence) != 20:
            logger.warning(f"gRNA length {len(grna_sequence)} != 20, results may be inaccurate")
        
        logger.info(f"Scoring efficiency for gRNA: {grna_sequence}")
        
        # Calculate individual algorithm scores
        doench2016 = self._calculate_doench2016(grna_sequence, context_sequence)
        doench2014 = self._calculate_doench2014(grna_sequence)
        wang2019 = self._calculate_wang2019(grna_sequence, context_sequence)
        kim2018 = self._calculate_kim2018(grna_sequence) if self.cas_type == "Cas12a" else 0.5
        
        # Calculate ensemble score
        if self.cas_type == "Cas9":
            weights = [0.4, 0.2, 0.3, 0.1]  # Doench2016, Doench2014, Wang2019, Kim2018
            scores = [doench2016, doench2014, wang2019, kim2018]
        elif self.cas_type == "Cas12a":
            weights = [0.3, 0.2, 0.2, 0.3]
            scores = [doench2016, doench2014, wang2019, kim2018]
        else:
            weights = [0.5, 0.3, 0.2, 0.0]
            scores = [doench2016, doench2014, wang2019, 0.5]
        
        ensemble = sum(w * s for w, s in zip(weights, scores))
        
        # Calculate confidence based on agreement between algorithms
        score_variance = np.var([doench2016, doench2014, wang2019])
        confidence = max(0.1, 1.0 - score_variance)
        
        return EfficiencyScore(
            grna_sequence=grna_sequence,
            doench2016_score=doench2016,
            doench2014_score=doench2014,
            wang2019_score=wang2019,
            kim2018_score=kim2018,
            ensemble_score=ensemble,
            confidence=confidence
        )
    
    def _calculate_doench2016(self, grna: str, context: str = None) -> float:
        """Calculate Doench 2016 (Rule Set 2) score"""
        if len(grna) != 20:
            return 0.5
        
        score = self.doench2016_params['intercept']
        
        # Position-specific nucleotide contributions
        for pos, nt in enumerate(grna):
            key = f"pos_{pos+1}_{nt}"
            if key in self.doench2016_params:
                score += self.doench2016_params[key]
        
        # Dinucleotide contributions
        for i in range(len(grna) - 1):
            dinuc = grna[i:i+2]
            key = f"dinuc_{i+1}_{dinuc}"
            if key in self.doench2016_params:
                score += self.doench2016_params[key]
        
        # GC content
        gc_content = GC(grna) / 100.0
        score += self.doench2016_params.get('gc_high', 0) * max(0, gc_content - 0.8)
        score += self.doench2016_params.get('gc_low', 0) * max(0, 0.2 - gc_content)
        
        # Convert to probability using logistic function
        probability = 1.0 / (1.0 + np.exp(-score))
        return min(max(probability, 0.0), 1.0)
    
    def _calculate_doench2014(self, grna: str) -> float:
        """Calculate Doench 2014 (Rule Set 1) score"""
        if len(grna) != 20:
            return 0.5
        
        score = 0.0
        
        # Position-specific scoring
        for i, nt in enumerate(grna):
            if i < len(self.doench2014_params['position_weights']):
                weight = self.doench2014_params['position_weights'][i]
                nt_score = self.doench2014_params['nucleotide_scores'].get(nt, 0.25)
                score += weight * nt_score
        
        # Penalize poly-T runs
        if 'TTTT' in grna:
            score *= 0.5
        
        # GC content penalty
        gc_content = GC(grna) / 100.0
        if gc_content < 0.2 or gc_content > 0.8:
            score *= 0.8
        
        return min(max(score, 0.0), 1.0)
    
    def _calculate_wang2019(self, grna: str, context: str = None) -> float:
        """Calculate Wang 2019 (DeepCRISPR-inspired) score"""
        if len(grna) != 20:
            return 0.5
        
        # Simplified deep learning-inspired scoring
        score = 0.5  # Base score
        
        # Sequence features
        features = self._extract_sequence_features(grna)
        
        # Apply learned weights (simplified)
        for feature, value in features.items():
            weight = self.wang2019_params.get(feature, 0.0)
            score += weight * value
        
        # Context features if available
        if context and len(context) >= 30:
            context_features = self._extract_context_features(context)
            for feature, value in context_features.items():
                weight = self.wang2019_params.get(feature, 0.0)
                score += weight * value
        
        return min(max(score, 0.0), 1.0)
    
    def _calculate_kim2018(self, grna: str) -> float:
        """Calculate Kim 2018 Cas12a efficiency score"""
        if len(grna) != 20:
            return 0.5
        
        score = self.kim2018_params['base_score']
        
        # Cas12a-specific features
        # Prefer T-rich regions at 5' end
        t_content_5prime = grna[:5].count('T') / 5.0
        score += self.kim2018_params['t_5prime_weight'] * t_content_5prime
        
        # Prefer G/C at 3' end
        gc_content_3prime = sum(1 for nt in grna[-5:] if nt in 'GC') / 5.0
        score += self.kim2018_params['gc_3prime_weight'] * gc_content_3prime
        
        # Penalize secondary structures
        hairpin_penalty = self._calculate_hairpin_penalty(grna)
        score *= (1.0 - hairpin_penalty)
        
        return min(max(score, 0.0), 1.0)
    
    def _extract_sequence_features(self, sequence: str) -> Dict[str, float]:
        """Extract sequence features for machine learning models"""
        features = {}
        
        # Nucleotide composition
        for nt in 'ATGC':
            features[f'{nt}_content'] = sequence.count(nt) / len(sequence)
        
        # Dinucleotide composition
        for i in range(len(sequence) - 1):
            dinuc = sequence[i:i+2]
            key = f'dinuc_{dinuc}'
            features[key] = features.get(key, 0) + 1
        
        # Normalize dinucleotide counts
        total_dinucs = len(sequence) - 1
        for key in list(features.keys()):
            if key.startswith('dinuc_'):
                features[key] /= total_dinucs
        
        # Position-specific features
        for i, nt in enumerate(sequence):
            features[f'pos_{i}_{nt}'] = 1.0
        
        return features
    
    def _extract_context_features(self, context: str) -> Dict[str, float]:
        """Extract context sequence features"""
        features = {}
        
        # Overall GC content
        features['context_gc'] = GC(context) / 100.0
        
        # Repetitive elements
        features['context_complexity'] = len(set(context)) / len(context)
        
        # Secondary structure potential
        features['context_hairpin'] = self._calculate_hairpin_penalty(context)
        
        return features
    
    def _calculate_hairpin_penalty(self, sequence: str) -> float:
        """Calculate penalty for potential hairpin formation"""
        # Simplified hairpin detection
        penalty = 0.0
        
        for i in range(len(sequence) - 6):
            for j in range(i + 4, min(i + 15, len(sequence) - 3)):
                # Check for complementary regions
                region1 = sequence[i:i+3]
                region2 = str(Seq(sequence[j:j+3]).reverse_complement())
                
                if region1 == region2:
                    penalty += 0.1
        
        return min(penalty, 0.5)
    
    def _init_doench2016_params(self) -> Dict:
        """Initialize Doench 2016 parameters"""
        # Simplified parameter set (real implementation would use full trained model)
        params = {
            'intercept': 0.59763615,
            'gc_high': -0.2026259,
            'gc_low': -0.1665878
        }
        
        # Position-specific nucleotide scores (simplified)
        for pos in range(1, 21):
            for nt in 'ATGC':
                if pos <= 5:  # 5' region
                    params[f'pos_{pos}_{nt}'] = 0.1 if nt in 'AT' else -0.1
                elif pos >= 17:  # 3' region (seed)
                    params[f'pos_{pos}_{nt}'] = 0.2 if nt in 'GC' else -0.2
                else:  # Middle region
                    params[f'pos_{pos}_{nt}'] = 0.05 if nt in 'GC' else -0.05
        
        return params
    
    def _init_doench2014_params(self) -> Dict:
        """Initialize Doench 2014 parameters"""
        return {
            'position_weights': [0.0, 0.0, 0.014, 0.0, 0.0, 0.395, 0.317, 0.0, 0.389, 0.079,
                               0.445, 0.508, 0.613, 0.851, 0.732, 0.828, 0.615, 0.804, 0.685, 0.583],
            'nucleotide_scores': {'A': 0.25, 'T': 0.25, 'G': 0.35, 'C': 0.35}
        }
    
    def _init_wang2019_params(self) -> Dict:
        """Initialize Wang 2019 parameters"""
        # Simplified deep learning-inspired parameters
        params = {}
        
        # Nucleotide content weights
        params['A_content'] = -0.1
        params['T_content'] = -0.2
        params['G_content'] = 0.15
        params['C_content'] = 0.15
        
        # Context weights
        params['context_gc'] = 0.1
        params['context_complexity'] = 0.2
        params['context_hairpin'] = -0.3
        
        return params
    
    def _init_kim2018_params(self) -> Dict:
        """Initialize Kim 2018 Cas12a parameters"""
        return {
            'base_score': 0.5,
            't_5prime_weight': 0.2,
            'gc_3prime_weight': 0.15
        }
    
    def batch_score(self, grna_sequences: List[str], 
                   context_sequences: List[str] = None) -> List[EfficiencyScore]:
        """Score multiple gRNAs in batch"""
        if context_sequences is None:
            context_sequences = [None] * len(grna_sequences)
        
        scores = []
        for grna, context in zip(grna_sequences, context_sequences):
            score = self.score_efficiency(grna, context)
            scores.append(score)
        
        return scores
