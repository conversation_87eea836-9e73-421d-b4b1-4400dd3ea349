"""
Off-Target Predictor - Advanced Off-Target Analysis for CRISPR Systems
Provides comprehensive off-target prediction with multiple scoring algorithms
"""

import re
import numpy as np
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
from Bio.Seq import Seq
from Bio.SeqUtils import GC
import logging

logger = logging.getLogger(__name__)

@dataclass
class OffTargetSite:
    """Off-target site data structure"""
    sequence: str
    position: int
    chromosome: str
    strand: str
    mismatches: int
    mismatch_positions: List[int]
    score: float
    cfd_score: float  # Cutting Frequency Determination score
    mit_score: float  # MIT score
    annotation: str  # Gene annotation if available

class OffTargetPredictor:
    """
    Advanced off-target prediction system with multiple scoring algorithms
    """
    
    def __init__(self, reference_genome: Optional[str] = None):
        self.reference_genome = reference_genome
        
        # CFD (Cutting Frequency Determination) scoring matrix
        self.cfd_matrix = self._init_cfd_matrix()
        
        # MIT scoring parameters
        self.mit_params = {
            'M': 0,      # Perfect match
            'e': 0.014,  # Energy parameter
            'weights': [0, 0, 0.014, 0, 0, 0.395, 0.317, 0, 0.389, 0.079,
                       0.445, 0.508, 0.613, 0.851, 0.732, 0.828, 0.615, 0.804, 0.685, 0.583]
        }
        
        # Position-specific mismatch penalties
        self.position_penalties = {
            'seed_region': (16, 20),  # Positions 17-20 (1-indexed)
            'seed_penalty': 10.0,     # High penalty for seed mismatches
            'pam_proximal': (18, 20), # PAM-proximal region
            'pam_penalty': 5.0        # Medium penalty for PAM-proximal mismatches
        }
        
    def predict_off_targets(self, grna_sequence: str, target_sequence: str = None,
                          max_mismatches: int = 4, include_bulges: bool = False) -> List[OffTargetSite]:
        """
        Predict off-target sites for a guide RNA
        
        Args:
            grna_sequence: Guide RNA sequence (20 nt)
            target_sequence: Target genome sequence (optional)
            max_mismatches: Maximum number of mismatches to consider
            include_bulges: Whether to include DNA/RNA bulges
            
        Returns:
            List of OffTargetSite objects sorted by score
        """
        logger.info(f"Predicting off-targets for gRNA: {grna_sequence}")
        
        if len(grna_sequence) != 20:
            raise ValueError("Guide RNA must be 20 nucleotides long")
        
        off_targets = []
        
        if target_sequence:
            # Search in provided sequence
            off_targets.extend(self._search_sequence(grna_sequence, target_sequence, 
                                                   max_mismatches, "custom", include_bulges))
        elif self.reference_genome:
            # Search in reference genome (placeholder for genome-wide search)
            off_targets.extend(self._search_genome(grna_sequence, max_mismatches, include_bulges))
        else:
            # Generate synthetic off-targets for demonstration
            off_targets.extend(self._generate_synthetic_offtargets(grna_sequence, max_mismatches))
        
        # Score all off-targets
        for site in off_targets:
            site.cfd_score = self._calculate_cfd_score(grna_sequence, site.sequence, site.mismatch_positions)
            site.mit_score = self._calculate_mit_score(grna_sequence, site.sequence, site.mismatch_positions)
            site.score = (site.cfd_score + site.mit_score) / 2  # Combined score
        
        # Sort by score (highest first)
        off_targets.sort(key=lambda x: x.score, reverse=True)
        
        logger.info(f"Found {len(off_targets)} potential off-target sites")
        return off_targets
    
    def _search_sequence(self, grna: str, sequence: str, max_mismatches: int, 
                        chromosome: str, include_bulges: bool) -> List[OffTargetSite]:
        """Search for off-targets in a specific sequence"""
        sites = []
        grna_len = len(grna)
        
        # Search both strands
        for strand, seq in [('+', sequence), ('-', str(Seq(sequence).reverse_complement()))]:
            for i in range(len(seq) - grna_len + 1):
                target_seq = seq[i:i + grna_len]
                
                # Calculate mismatches
                mismatches = []
                mismatch_count = 0
                
                for j, (g, t) in enumerate(zip(grna, target_seq)):
                    if g != t:
                        mismatches.append(j)
                        mismatch_count += 1
                
                # Check if within mismatch threshold
                if mismatch_count <= max_mismatches:
                    position = i if strand == '+' else len(sequence) - i - grna_len
                    
                    site = OffTargetSite(
                        sequence=target_seq,
                        position=position,
                        chromosome=chromosome,
                        strand=strand,
                        mismatches=mismatch_count,
                        mismatch_positions=mismatches,
                        score=0.0,  # Will be calculated later
                        cfd_score=0.0,
                        mit_score=0.0,
                        annotation=""
                    )
                    sites.append(site)
        
        return sites
    
    def _search_genome(self, grna: str, max_mismatches: int, include_bulges: bool) -> List[OffTargetSite]:
        """Search for off-targets in reference genome (placeholder)"""
        # This would implement genome-wide search using tools like BWA or Bowtie
        # For now, return empty list as placeholder
        logger.warning("Genome-wide search not implemented. Use target_sequence parameter.")
        return []
    
    def _generate_synthetic_offtargets(self, grna: str, max_mismatches: int) -> List[OffTargetSite]:
        """Generate synthetic off-targets for demonstration"""
        sites = []
        
        # Generate variants with different numbers of mismatches
        for num_mismatches in range(1, max_mismatches + 1):
            for _ in range(min(10, 4 ** num_mismatches)):  # Limit number of variants
                variant = self._create_variant(grna, num_mismatches)
                mismatch_positions = [i for i, (a, b) in enumerate(zip(grna, variant)) if a != b]
                
                site = OffTargetSite(
                    sequence=variant,
                    position=np.random.randint(1000, 100000),
                    chromosome=f"chr{np.random.randint(1, 23)}",
                    strand=np.random.choice(['+', '-']),
                    mismatches=num_mismatches,
                    mismatch_positions=mismatch_positions,
                    score=0.0,
                    cfd_score=0.0,
                    mit_score=0.0,
                    annotation=f"Gene_{np.random.randint(1, 1000)}"
                )
                sites.append(site)
        
        return sites
    
    def _create_variant(self, sequence: str, num_mismatches: int) -> str:
        """Create a sequence variant with specified number of mismatches"""
        variant = list(sequence)
        nucleotides = ['A', 'T', 'G', 'C']
        
        positions = np.random.choice(len(sequence), num_mismatches, replace=False)
        
        for pos in positions:
            original = variant[pos]
            new_nt = np.random.choice([nt for nt in nucleotides if nt != original])
            variant[pos] = new_nt
        
        return ''.join(variant)
    
    def _calculate_cfd_score(self, grna: str, target: str, mismatch_positions: List[int]) -> float:
        """Calculate CFD (Cutting Frequency Determination) score"""
        if not mismatch_positions:
            return 1.0  # Perfect match
        
        score = 1.0
        
        for pos in mismatch_positions:
            if pos < len(self.cfd_matrix):
                grna_nt = grna[pos]
                target_nt = target[pos]
                
                if grna_nt in self.cfd_matrix[pos] and target_nt in self.cfd_matrix[pos][grna_nt]:
                    penalty = self.cfd_matrix[pos][grna_nt][target_nt]
                    score *= penalty
                else:
                    score *= 0.1  # Default penalty for unknown combinations
        
        return max(0.0, score)
    
    def _calculate_mit_score(self, grna: str, target: str, mismatch_positions: List[int]) -> float:
        """Calculate MIT off-target score"""
        if not mismatch_positions:
            return 1.0  # Perfect match
        
        # Calculate mean pairwise distance between mismatches
        if len(mismatch_positions) > 1:
            distances = []
            for i in range(len(mismatch_positions)):
                for j in range(i + 1, len(mismatch_positions)):
                    distances.append(abs(mismatch_positions[i] - mismatch_positions[j]))
            mean_distance = np.mean(distances) if distances else 1
        else:
            mean_distance = 1
        
        # Calculate score using MIT formula
        score = 1.0
        for pos in mismatch_positions:
            if pos < len(self.mit_params['weights']):
                weight = self.mit_params['weights'][pos]
                score *= (1 - weight)
        
        # Apply distance penalty
        distance_penalty = 1.0 / (((19 - mean_distance) / 19) * 4 + 1)
        score *= distance_penalty
        
        # Apply additional penalties for seed region mismatches
        seed_start, seed_end = self.position_penalties['seed_region']
        seed_mismatches = [pos for pos in mismatch_positions if seed_start <= pos < seed_end]
        if seed_mismatches:
            score *= (1.0 / self.position_penalties['seed_penalty']) ** len(seed_mismatches)
        
        return max(0.0, score)
    
    def _init_cfd_matrix(self) -> Dict:
        """Initialize CFD scoring matrix"""
        # Simplified CFD matrix (real implementation would use full experimental data)
        cfd_matrix = {}
        
        for pos in range(20):
            cfd_matrix[pos] = {}
            for grna_nt in ['A', 'T', 'G', 'C']:
                cfd_matrix[pos][grna_nt] = {}
                for target_nt in ['A', 'T', 'G', 'C']:
                    if grna_nt == target_nt:
                        cfd_matrix[pos][grna_nt][target_nt] = 1.0  # Perfect match
                    else:
                        # Position-dependent mismatch penalties
                        if pos >= 16:  # Seed region
                            cfd_matrix[pos][grna_nt][target_nt] = 0.1
                        elif pos >= 10:
                            cfd_matrix[pos][grna_nt][target_nt] = 0.3
                        else:
                            cfd_matrix[pos][grna_nt][target_nt] = 0.6
        
        return cfd_matrix
    
    def filter_by_score(self, off_targets: List[OffTargetSite], 
                       min_score: float = 0.1) -> List[OffTargetSite]:
        """Filter off-targets by minimum score threshold"""
        return [site for site in off_targets if site.score >= min_score]
    
    def get_high_risk_sites(self, off_targets: List[OffTargetSite], 
                           top_n: int = 10) -> List[OffTargetSite]:
        """Get top N highest-scoring (most risky) off-target sites"""
        sorted_sites = sorted(off_targets, key=lambda x: x.score, reverse=True)
        return sorted_sites[:top_n]
