"""
PAM Recognizer - Advanced PAM Sequence Recognition and Analysis
Provides comprehensive PAM recognition for different Cas proteins with scoring
"""

import re
import numpy as np
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
from Bio.Seq import Seq
import logging

logger = logging.getLogger(__name__)

@dataclass
class PAMSite:
    """PAM site data structure"""
    sequence: str
    position: int
    strand: str  # '+' or '-'
    cas_type: str
    score: float
    context: str  # Surrounding sequence context
    accessibility: float  # Chromatin accessibility score

class PAMRecognizer:
    """
    Advanced PAM sequence recognition system for multiple Cas proteins
    """
    
    def __init__(self):
        # Comprehensive PAM patterns for different Cas proteins
        self.pam_patterns = {
            "Cas9": {
                "pattern": r"[ATCG]GG",  # NGG PAM
                "position": "3prime",  # PAM is 3' to gRNA
                "length": 3,
                "preferences": {"AGG": 1.0, "TGG": 0.9, "CGG": 0.8, "GGG": 0.7}
            },
            "Cas9-NG": {
                "pattern": r"[ATCG]G[ATCG]",  # NGN PAM (engineered Cas9)
                "position": "3prime",
                "length": 3,
                "preferences": {"AGA": 0.8, "AGT": 0.8, "TGA": 0.7, "TGT": 0.7}
            },
            "Cas12a": {
                "pattern": r"TTT[ATCGV]",  # TTTV PAM (V = A, C, G)
                "position": "5prime",  # PAM is 5' to gRNA
                "length": 4,
                "preferences": {"TTTA": 1.0, "TTTC": 0.9, "TTTG": 0.8}
            },
            "Cas12b": {
                "pattern": r"TTT[ATCG]",  # TTTN PAM
                "position": "5prime",
                "length": 4,
                "preferences": {"TTTA": 1.0, "TTTC": 0.9, "TTTG": 0.8, "TTTT": 0.7}
            },
            "Cas12f": {
                "pattern": r"TT[ATCG]",  # TTN PAM
                "position": "5prime", 
                "length": 3,
                "preferences": {"TTA": 1.0, "TTC": 0.9, "TTG": 0.8, "TTT": 0.7}
            },
            "Cas13a": {
                "pattern": r"[ATCG]{28}",  # No specific PAM requirement
                "position": "none",
                "length": 0,
                "preferences": {}
            },
            "Cas13b": {
                "pattern": r"[ATCG]{28}",  # No specific PAM requirement
                "position": "none", 
                "length": 0,
                "preferences": {}
            },
            "CasX": {
                "pattern": r"TTC[ATCG]",  # TTCN PAM
                "position": "5prime",
                "length": 4,
                "preferences": {"TTCA": 1.0, "TTCC": 0.9, "TTCG": 0.8, "TTCT": 0.7}
            },
            "CasY": {
                "pattern": r"T[ATCG]",  # TN PAM
                "position": "5prime",
                "length": 2,
                "preferences": {"TA": 1.0, "TC": 0.9, "TG": 0.8, "TT": 0.7}
            }
        }
        
        # Context scoring parameters
        self.context_weights = {
            "gc_content": 0.3,
            "secondary_structure": 0.2,
            "accessibility": 0.3,
            "conservation": 0.2
        }
        
    def find_pam_sites(self, sequence: str, cas_type: str = "Cas9", 
                      include_context: bool = True) -> List[PAMSite]:
        """
        Find all PAM sites in a sequence for specified Cas protein
        
        Args:
            sequence: DNA sequence to search
            cas_type: Type of Cas protein
            include_context: Whether to include context analysis
            
        Returns:
            List of PAMSite objects
        """
        if cas_type not in self.pam_patterns:
            raise ValueError(f"Unsupported Cas type: {cas_type}")
            
        logger.info(f"Finding {cas_type} PAM sites in {len(sequence)}bp sequence")
        
        pam_info = self.pam_patterns[cas_type]
        sites = []
        
        # Search forward strand
        sites.extend(self._search_strand(sequence, cas_type, "+", include_context))
        
        # Search reverse strand
        rev_sequence = str(Seq(sequence).reverse_complement())
        rev_sites = self._search_strand(rev_sequence, cas_type, "-", include_context)
        
        # Adjust positions for reverse strand
        for site in rev_sites:
            site.position = len(sequence) - site.position - len(site.sequence)
        
        sites.extend(rev_sites)
        
        # Sort by position
        sites.sort(key=lambda x: x.position)
        
        logger.info(f"Found {len(sites)} {cas_type} PAM sites")
        return sites
    
    def _search_strand(self, sequence: str, cas_type: str, strand: str, 
                      include_context: bool) -> List[PAMSite]:
        """Search for PAM sites on a single strand"""
        sites = []
        pam_info = self.pam_patterns[cas_type]
        pattern = pam_info["pattern"]
        
        # Handle special case for Cas13 (no PAM requirement)
        if cas_type.startswith("Cas13"):
            # For Cas13, create sites every 28 bp (typical gRNA length)
            for i in range(0, len(sequence) - 28, 10):  # Every 10 bp for coverage
                context = sequence[max(0, i-10):i+38] if include_context else ""
                site = PAMSite(
                    sequence="",  # No PAM sequence
                    position=i,
                    strand=strand,
                    cas_type=cas_type,
                    score=1.0,  # No PAM scoring for Cas13
                    context=context,
                    accessibility=0.8  # Default accessibility
                )
                sites.append(site)
            return sites
        
        # Search for PAM pattern
        for match in re.finditer(pattern, sequence):
            pam_seq = match.group()
            position = match.start()
            
            # Calculate PAM score based on preferences
            score = self._calculate_pam_score(pam_seq, cas_type)
            
            # Get context if requested
            context = ""
            accessibility = 0.8  # Default
            if include_context:
                context_start = max(0, position - 20)
                context_end = min(len(sequence), position + len(pam_seq) + 20)
                context = sequence[context_start:context_end]
                accessibility = self._calculate_accessibility(context)
            
            site = PAMSite(
                sequence=pam_seq,
                position=position,
                strand=strand,
                cas_type=cas_type,
                score=score,
                context=context,
                accessibility=accessibility
            )
            sites.append(site)
            
        return sites
    
    def _calculate_pam_score(self, pam_sequence: str, cas_type: str) -> float:
        """Calculate PAM quality score based on preferences"""
        preferences = self.pam_patterns[cas_type]["preferences"]
        
        if not preferences:
            return 1.0  # No preferences defined
            
        # Direct lookup if available
        if pam_sequence in preferences:
            return preferences[pam_sequence]
        
        # Fuzzy matching for partial matches
        best_score = 0.0
        for pref_pam, score in preferences.items():
            if len(pref_pam) == len(pam_sequence):
                matches = sum(1 for a, b in zip(pam_sequence, pref_pam) if a == b)
                similarity = matches / len(pam_sequence)
                fuzzy_score = score * similarity
                best_score = max(best_score, fuzzy_score)
        
        return best_score if best_score > 0 else 0.5  # Default score
    
    def _calculate_accessibility(self, context: str) -> float:
        """Calculate chromatin accessibility score based on sequence context"""
        if not context:
            return 0.8
            
        # Simple accessibility model based on GC content and repeats
        from Bio.SeqUtils import GC
        
        gc_content = GC(context) / 100.0
        
        # Optimal GC content around 40-60%
        gc_score = 1.0 - abs(gc_content - 0.5) * 2
        
        # Penalize repetitive sequences
        repeat_penalty = 1.0
        for i in range(len(context) - 3):
            kmer = context[i:i+4]
            if context.count(kmer) > 2:
                repeat_penalty *= 0.9
        
        accessibility = gc_score * repeat_penalty
        return max(0.1, min(1.0, accessibility))
    
    def get_supported_cas_types(self) -> List[str]:
        """Get list of supported Cas protein types"""
        return list(self.pam_patterns.keys())
    
    def get_pam_info(self, cas_type: str) -> Dict:
        """Get PAM pattern information for a Cas type"""
        if cas_type not in self.pam_patterns:
            raise ValueError(f"Unsupported Cas type: {cas_type}")
        return self.pam_patterns[cas_type].copy()
